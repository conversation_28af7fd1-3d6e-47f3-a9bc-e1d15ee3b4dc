#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const zlib = require('zlib');

/**
 * MRP图片解压缩工具 - 解压缩GZIP压缩的BMP文件
 * 作者: Claude 4.0 sonnet
 */
class ImageDecompressor {
    constructor() {
        this.processedCount = 0;
        this.successCount = 0;
    }

    /**
     * 检查文件是否为GZIP压缩格式
     */
    isGzipFile(filePath) {
        try {
            const buffer = fs.readFileSync(filePath);
            // GZIP文件以 1F 8B 开头
            return buffer.length >= 2 && buffer[0] === 0x1F && buffer[1] === 0x8B;
        } catch (error) {
            return false;
        }
    }

    /**
     * 解压缩单个文件
     */
    async decompressFile(inputPath, outputPath) {
        try {
            console.log(`🔄 解压缩: ${path.basename(inputPath)}`);
            
            // 读取压缩文件
            const compressedData = fs.readFileSync(inputPath);
            
            // 使用zlib解压缩
            const decompressedData = zlib.gunzipSync(compressedData);
            
            // 保存解压后的文件
            fs.writeFileSync(outputPath, decompressedData);
            
            console.log(`   ✅ 解压成功: ${decompressedData.length} 字节`);
            
            // 分析解压后的文件格式
            this.analyzeDecompressedFile(outputPath, decompressedData);
            
            this.successCount++;
            return true;
            
        } catch (error) {
            console.error(`   ❌ 解压失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 分析解压后的文件格式
     */
    analyzeDecompressedFile(filePath, data) {
        if (data.length < 16) {
            console.log(`   ℹ️  文件太小，无法分析格式`);
            return;
        }

        // 检查是否为标准BMP格式
        if (data[0] === 0x42 && data[1] === 0x4D) { // "BM"
            const fileSize = data.readUInt32LE(2);
            const dataOffset = data.readUInt32LE(10);
            const width = data.readUInt32LE(18);
            const height = data.readUInt32LE(22);
            const bitsPerPixel = data.readUInt16LE(28);
            
            console.log(`   📷 标准BMP格式:`);
            console.log(`      尺寸: ${width}x${height}`);
            console.log(`      色深: ${bitsPerPixel}位`);
            console.log(`      文件大小: ${fileSize} 字节`);
        } else {
            // 显示文件头部的十六进制数据
            const header = data.slice(0, Math.min(16, data.length));
            const hexString = Array.from(header)
                .map(b => b.toString(16).padStart(2, '0'))
                .join(' ');
            console.log(`   🔍 未知格式，头部: ${hexString}`);
            
            // 尝试识别其他可能的格式
            this.identifyImageFormat(data);
        }
    }

    /**
     * 尝试识别图片格式
     */
    identifyImageFormat(data) {
        // PNG格式检查
        if (data.length >= 8 && 
            data[0] === 0x89 && data[1] === 0x50 && data[2] === 0x4E && data[3] === 0x47) {
            console.log(`   📷 PNG格式检测到`);
            return 'PNG';
        }
        
        // JPEG格式检查
        if (data.length >= 2 && data[0] === 0xFF && data[1] === 0xD8) {
            console.log(`   📷 JPEG格式检测到`);
            return 'JPEG';
        }
        
        // 可能是自定义的图片格式
        console.log(`   ❓ 可能是自定义图片格式`);
        return 'UNKNOWN';
    }

    /**
     * 批量解压缩目录中的所有图片文件
     */
    async decompressDirectory(inputDir, outputDir) {
        console.log('🐾 Claude 4.0 sonnet 图片解压缩工具');
        console.log('=' .repeat(50));

        // 创建输出目录
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        // 递归查找所有图片文件
        const imageFiles = [];
        this.findImageFiles(inputDir, imageFiles);

        if (imageFiles.length === 0) {
            console.log('❌ 没有找到图片文件');
            return false;
        }

        console.log(`📁 找到 ${imageFiles.length} 个图片文件`);

        // 批量处理
        for (const inputPath of imageFiles) {
            this.processedCount++;
            
            // 检查是否为GZIP压缩文件
            if (!this.isGzipFile(inputPath)) {
                console.log(`⏭️  跳过非压缩文件: ${path.basename(inputPath)}`);
                continue;
            }

            // 生成输出路径
            const relativePath = path.relative(inputDir, inputPath);
            const outputPath = path.join(outputDir, relativePath);
            
            // 确保输出目录存在
            const outputDirPath = path.dirname(outputPath);
            if (!fs.existsSync(outputDirPath)) {
                fs.mkdirSync(outputDirPath, { recursive: true });
            }

            // 解压缩文件
            await this.decompressFile(inputPath, outputPath);
        }

        console.log('\n' + '=' .repeat(50));
        console.log(`🎉 解压缩完成! 成功: ${this.successCount}/${this.processedCount}`);
        
        if (this.successCount > 0) {
            console.log(`📂 解压后的文件保存在: ${path.resolve(outputDir)}`);
        }

        return this.successCount > 0;
    }

    /**
     * 递归查找图片文件
     */
    findImageFiles(dir, fileList) {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                this.findImageFiles(fullPath, fileList);
            } else {
                const ext = path.extname(item).toLowerCase();
                if (['.bmp', '.png', '.jpg', '.jpeg', '.gif'].includes(ext)) {
                    fileList.push(fullPath);
                }
            }
        }
    }
}

// 命令行接口
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('🐾 Claude 4.0 sonnet 图片解压缩工具');
        console.log('用法: node image-decompressor.js <输入目录> [输出目录]');
        console.log('');
        console.log('示例:');
        console.log('  node image-decompressor.js ./extracted');
        console.log('  node image-decompressor.js ./extracted ./decompressed');
        process.exit(1);
    }

    const inputDir = args[0];
    const outputDir = args[1] || './decompressed';

    if (!fs.existsSync(inputDir)) {
        console.error(`❌ 输入目录不存在: ${inputDir}`);
        process.exit(1);
    }

    const decompressor = new ImageDecompressor();
    const success = await decompressor.decompressDirectory(inputDir, outputDir);
    
    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = ImageDecompressor;
