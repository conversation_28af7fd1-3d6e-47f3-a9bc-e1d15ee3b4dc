{"name": "mrp-extractor", "version": "1.0.0", "description": "A Node.js tool to extract resources from MRP (MiniJ platform) game files", "main": "mrp-extractor.js", "scripts": {"start": "node mrp-extractor.js", "extract": "node mrp-extractor.js"}, "keywords": ["mrp", "minij", "game", "extractor", "binary", "archive"], "author": "Claude 4.0 sonnet", "license": "MIT", "engines": {"node": ">=12.0.0"}, "bin": {"mrp-extract": "./mrp-extractor.js"}}