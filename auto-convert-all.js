const fs = require('fs');
const path = require('path');
const zlib = require('zlib');

class AutoMRPConverter {
    constructor() {
        this.totalMrpFiles = 0;
        this.processedMrpFiles = 0;
        this.totalImages = 0;
        this.convertedImages = 0;
        this.errors = [];
        this.startTime = Date.now();
    }

    /**
     * 主入口 - 全自动转换所有MRP文件
     */
    async convertAll(inputDir = 'gwy', outputDir = 'converted_all') {
        console.log(`🐾 Claude 4.0 sonnet 全自动MRP转换工具`);
        console.log(`==================================================`);
        console.log(`📂 扫描目录: ${inputDir}`);
        console.log(`📂 输出目录: ${outputDir}`);
        console.log(`⏰ 开始时间: ${new Date().toLocaleString()}`);
        console.log(`==================================================\n`);

        // 创建输出目录
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        // 扫描所有MRP文件
        const mrpFiles = this.findMrpFiles(inputDir);
        this.totalMrpFiles = mrpFiles.length;
        
        console.log(`🔍 找到 ${this.totalMrpFiles} 个MRP文件:`);
        mrpFiles.forEach((file, index) => {
            console.log(`   ${index + 1}. ${file}`);
        });
        console.log('');

        // 处理每个MRP文件
        for (const mrpFile of mrpFiles) {
            await this.processMrpFile(mrpFile, outputDir);
        }

        // 生成最终报告
        this.generateReport();
    }

    /**
     * 扫描目录找到所有MRP文件
     */
    findMrpFiles(dir) {
        const mrpFiles = [];
        
        if (!fs.existsSync(dir)) {
            console.error(`❌ 目录不存在: ${dir}`);
            return mrpFiles;
        }

        const items = fs.readdirSync(dir);
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isFile() && item.toLowerCase().endsWith('.mrp')) {
                mrpFiles.push(fullPath);
            } else if (stat.isDirectory()) {
                // 递归扫描子目录
                mrpFiles.push(...this.findMrpFiles(fullPath));
            }
        }
        
        return mrpFiles;
    }

    /**
     * 处理单个MRP文件
     */
    async processMrpFile(mrpPath, outputDir) {
        const mrpName = path.basename(mrpPath, '.mrp');
        console.log(`🚀 [${this.processedMrpFiles + 1}/${this.totalMrpFiles}] 处理: ${mrpName}.mrp`);
        
        try {
            // 1. 解包MRP
            const tempDir = `temp_${mrpName}_${Date.now()}`;
            const extractedDir = await this.extractMrp(mrpPath, tempDir);
            
            // 2. 解压缩文件
            const decompressedDir = `${tempDir}_decompressed`;
            await this.decompressFiles(extractedDir, decompressedDir);
            
            // 3. 转换图片
            const finalOutputDir = path.join(outputDir, mrpName);
            await this.convertImages(decompressedDir, finalOutputDir);
            
            // 4. 清理临时文件
            this.cleanupTemp([tempDir, decompressedDir]);
            
            this.processedMrpFiles++;
            console.log(`   ✅ ${mrpName} 处理完成\n`);
            
        } catch (error) {
            console.error(`   ❌ ${mrpName} 处理失败: ${error.message}\n`);
            this.errors.push({ file: mrpName, error: error.message });
        }
    }

    /**
     * 解包MRP文件
     */
    async extractMrp(mrpPath, outputDir) {
        console.log(`   📦 解包中...`);
        
        const data = fs.readFileSync(mrpPath);
        
        // 检查MRP魔数
        if (data.toString('ascii', 0, 4) !== 'MRPG') {
            throw new Error('不是有效的MRP文件');
        }

        // 读取文件头
        const version = data.readUInt32LE(4);
        const totalSize = data.readUInt32LE(8);
        const headerInfo = data.readUInt32LE(12);
        
        // 解析目录表
        let offset = 16;
        const files = [];
        
        while (offset < data.length) {
            const nameLength = data.readUInt8(offset);
            if (nameLength === 0) break;
            
            offset++;
            const fileName = data.toString('ascii', offset, offset + nameLength);
            offset += nameLength;
            
            const fileOffset = data.readUInt32LE(offset);
            const fileSize = data.readUInt32LE(offset + 4);
            offset += 8;
            
            // 验证文件数据
            if (fileOffset + fileSize <= data.length) {
                files.push({ name: fileName, offset: fileOffset, size: fileSize });
            }
        }
        
        // 创建输出目录
        const fullOutputDir = path.join(outputDir, path.basename(mrpPath, '.mrp'));
        if (!fs.existsSync(fullOutputDir)) {
            fs.mkdirSync(fullOutputDir, { recursive: true });
        }
        
        // 提取文件
        for (const file of files) {
            const fileData = data.slice(file.offset, file.offset + file.size);
            const outputPath = path.join(fullOutputDir, file.name);
            fs.writeFileSync(outputPath, fileData);
        }
        
        console.log(`   📦 解包完成: ${files.length} 个文件`);
        return fullOutputDir;
    }

    /**
     * 解压缩文件
     */
    async decompressFiles(inputDir, outputDir) {
        console.log(`   🗜️  解压缩中...`);
        
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        
        let decompressedCount = 0;
        this.processDirectory(inputDir, outputDir, (inputPath, outputPath) => {
            const data = fs.readFileSync(inputPath);
            
            // 检查是否为GZIP压缩
            if (data.length >= 2 && data[0] === 0x1f && data[1] === 0x8b) {
                try {
                    const decompressed = zlib.gunzipSync(data);
                    fs.writeFileSync(outputPath, decompressed);
                    decompressedCount++;
                } catch (error) {
                    // 解压失败，复制原文件
                    fs.writeFileSync(outputPath, data);
                }
            } else {
                // 非压缩文件，直接复制
                fs.writeFileSync(outputPath, data);
            }
        });
        
        console.log(`   🗜️  解压缩完成: ${decompressedCount} 个文件`);
    }

    /**
     * 转换图片文件
     */
    async convertImages(inputDir, outputDir) {
        console.log(`   🖼️  转换图片中...`);
        
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        
        let convertedCount = 0;
        this.processDirectory(inputDir, outputDir, (inputPath, outputPath) => {
            const ext = path.extname(inputPath).toLowerCase();
            if (['.bmp', '.png', '.jpg', '.jpeg'].includes(ext)) {
                if (this.convertSingleImage(inputPath, outputPath)) {
                    convertedCount++;
                    this.convertedImages++;
                }
                this.totalImages++;
            }
        });
        
        console.log(`   🖼️  图片转换完成: ${convertedCount} 个文件`);
    }

    /**
     * 转换单个图片文件
     */
    convertSingleImage(inputPath, outputPath) {
        try {
            const data = fs.readFileSync(inputPath);

            // 猜测图片尺寸
            const dimensions = this.guessImageDimensions(data.length);
            const { width, height } = dimensions;

            // 创建BMP文件
            const bmpHeader = this.createBmpHeader(width, height);
            const imageData = this.convertRgb565ToBmp(data, width, height);

            // 写入BMP文件
            const bmpData = Buffer.concat([bmpHeader, imageData]);
            const bmpOutputPath = outputPath.replace(/\.[^.]+$/, '.bmp');
            fs.writeFileSync(bmpOutputPath, bmpData);

            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * 猜测图片尺寸
     */
    guessImageDimensions(fileSize) {
        const pixelCount = fileSize / 2; // RGB565每像素2字节

        // 常见尺寸（基于之前的分析结果）
        const commonDimensions = [
            [240, 320], [320, 240], [189, 200], [200, 189], [128, 150], [150, 128],
            [128, 128], [126, 145], [145, 126], [120, 134], [134, 120], [120, 128],
            [128, 120], [120, 80], [80, 120], [112, 133], [133, 112], [100, 50],
            [96, 100], [100, 96], [92, 96], [96, 92], [64, 96], [96, 64], [64, 64],
            [56, 70], [70, 56], [56, 64], [64, 56], [60, 60], [49, 78], [78, 49],
            [50, 80], [80, 50], [49, 76], [76, 49], [49, 60], [60, 49], [48, 49],
            [49, 48], [44, 49], [49, 44], [40, 50], [50, 40], [40, 45], [45, 40],
            [36, 44], [44, 36], [35, 58], [58, 35], [32, 32], [31, 40], [40, 31],
            [30, 60], [60, 30], [26, 77], [77, 26], [26, 30], [30, 26], [25, 20],
            [23, 23], [22, 26], [26, 22], [20, 32], [32, 20], [20, 20], [20, 10],
            [16, 16], [15, 16], [16, 15], [14, 16], [16, 14], [14, 14], [13, 35],
            [35, 13], [12, 16], [16, 12], [11, 22], [22, 11], [11, 14], [14, 11],
            [9, 11], [11, 9], [9, 9], [8, 8], [7, 9], [9, 7], [3, 7], [7, 3],
            [21, 3], [3, 21], [50, 25], [25, 50]
        ];

        // 检查常见尺寸
        for (const [w, h] of commonDimensions) {
            if (w * h === pixelCount) {
                return { width: w, height: h, confidence: 'high' };
            }
        }

        // 寻找最接近正方形的尺寸
        const sqrt = Math.sqrt(pixelCount);
        for (let width = Math.floor(sqrt); width >= 1; width--) {
            if (pixelCount % width === 0) {
                const height = pixelCount / width;
                return { width, height, confidence: 'medium' };
            }
        }

        return { width: Math.floor(sqrt), height: Math.ceil(sqrt), confidence: 'low' };
    }

    /**
     * 创建BMP文件头
     */
    createBmpHeader(width, height) {
        const rowBytes = width * 3;
        const paddedRowBytes = Math.ceil(rowBytes / 4) * 4;
        const imageSize = paddedRowBytes * height;
        const fileSize = 54 + imageSize;

        const header = Buffer.alloc(54);

        // BMP文件头
        header.write('BM', 0);
        header.writeUInt32LE(fileSize, 2);
        header.writeUInt32LE(0, 6);
        header.writeUInt32LE(54, 10);

        // DIB头
        header.writeUInt32LE(40, 14);
        header.writeInt32LE(width, 18);
        header.writeInt32LE(-height, 22); // 负值表示从上到下
        header.writeUInt16LE(1, 26);
        header.writeUInt16LE(24, 28);
        header.writeUInt32LE(0, 30);
        header.writeUInt32LE(imageSize, 34);
        header.writeUInt32LE(2835, 38);
        header.writeUInt32LE(2835, 42);
        header.writeUInt32LE(0, 46);
        header.writeUInt32LE(0, 50);

        return header;
    }

    /**
     * 转换RGB565到BMP格式
     */
    convertRgb565ToBmp(data, width, height) {
        const rowBytes = width * 3;
        const paddedRowBytes = Math.ceil(rowBytes / 4) * 4;
        const imageData = Buffer.alloc(paddedRowBytes * height);

        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const pixelIndex = y * width + x;
                const dataIndex = y * paddedRowBytes + x * 3;

                if (pixelIndex * 2 + 1 < data.length) {
                    const rgb565 = data.readUInt16LE(pixelIndex * 2);
                    const rgb = this.rgb565ToRgb888(rgb565);

                    // BMP格式是BGR顺序
                    imageData[dataIndex] = rgb.b;
                    imageData[dataIndex + 1] = rgb.g;
                    imageData[dataIndex + 2] = rgb.r;
                }
            }
        }

        return imageData;
    }

    /**
     * RGB565转RGB888
     */
    rgb565ToRgb888(rgb565) {
        const r = (rgb565 >> 11) & 0x1F;
        const g = (rgb565 >> 5) & 0x3F;
        const b = rgb565 & 0x1F;

        return {
            r: (r << 3) | (r >> 2),
            g: (g << 2) | (g >> 4),
            b: (b << 3) | (b >> 2)
        };
    }

    /**
     * 递归处理目录
     */
    processDirectory(inputDir, outputDir, fileProcessor) {
        const items = fs.readdirSync(inputDir);

        for (const item of items) {
            const inputPath = path.join(inputDir, item);
            const outputPath = path.join(outputDir, item);

            const stat = fs.statSync(inputPath);

            if (stat.isDirectory()) {
                if (!fs.existsSync(outputPath)) {
                    fs.mkdirSync(outputPath, { recursive: true });
                }
                this.processDirectory(inputPath, outputPath, fileProcessor);
            } else if (stat.isFile()) {
                fileProcessor(inputPath, outputPath);
            }
        }
    }

    /**
     * 清理临时文件
     */
    cleanupTemp(tempDirs) {
        for (const dir of tempDirs) {
            if (fs.existsSync(dir)) {
                this.removeDirectory(dir);
            }
        }
    }

    /**
     * 递归删除目录
     */
    removeDirectory(dir) {
        if (fs.existsSync(dir)) {
            const items = fs.readdirSync(dir);
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);

                if (stat.isDirectory()) {
                    this.removeDirectory(fullPath);
                } else {
                    fs.unlinkSync(fullPath);
                }
            }
            fs.rmdirSync(dir);
        }
    }

    /**
     * 生成最终报告
     */
    generateReport() {
        const endTime = Date.now();
        const duration = (endTime - this.startTime) / 1000;

        console.log(`\n==================================================`);
        console.log(`🎉 全自动转换完成！`);
        console.log(`==================================================`);
        console.log(`📊 处理统计:`);
        console.log(`   📦 MRP文件: ${this.processedMrpFiles}/${this.totalMrpFiles}`);
        console.log(`   🖼️  图片文件: ${this.convertedImages}/${this.totalImages}`);
        console.log(`   ⏱️  总耗时: ${duration.toFixed(2)} 秒`);
        console.log(`   ⚡ 平均速度: ${(this.convertedImages / duration).toFixed(2)} 图片/秒`);

        if (this.errors.length > 0) {
            console.log(`\n❌ 错误列表:`);
            this.errors.forEach((error, index) => {
                console.log(`   ${index + 1}. ${error.file}: ${error.error}`);
            });
        }

        console.log(`\n🎯 转换成功率: ${((this.convertedImages / this.totalImages) * 100).toFixed(1)}%`);
        console.log(`📂 所有转换后的文件保存在: converted_all 目录`);
        console.log(`⏰ 完成时间: ${new Date().toLocaleString()}`);
        console.log(`==================================================`);

        // 如果全部成功，输出庆祝信息
        if (this.errors.length === 0 && this.convertedImages === this.totalImages) {
            console.log(`\n🎊 完美！所有文件都转换成功！`);
            console.log(`🐾 Claude 4.0 sonnet 为您提供最专业的服务！`);
        }
    }
}

// 命令行使用
if (require.main === module) {
    const converter = new AutoMRPConverter();

    // 获取命令行参数
    const args = process.argv.slice(2);
    const inputDir = args[0] || 'gwy';
    const outputDir = args[1] || 'converted_all';

    // 开始转换
    converter.convertAll(inputDir, outputDir).catch(error => {
        console.error(`💥 致命错误: ${error.message}`);
        process.exit(1);
    });
}

module.exports = AutoMRPConverter;
