<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Claude 4.0 sonnet 智能图片分析器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .controls {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        .control-group input, .control-group select {
            padding: 8px;
            border-radius: 5px;
            border: none;
            margin-right: 10px;
        }
        
        .file-input {
            display: none;
        }
        
        .file-label {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-label:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .result-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .result-card img {
            max-width: 100%;
            max-height: 200px;
            border-radius: 8px;
            background: white;
            padding: 5px;
            image-rendering: pixelated;
        }
        
        .result-card h3 {
            margin: 10px 0 5px 0;
            font-size: 1em;
        }
        
        .result-card p {
            margin: 5px 0;
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .hex-display {
            background: rgba(0,0,0,0.3);
            padding: 10px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            margin: 10px 0;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Claude 4.0 sonnet 智能图片分析器</h1>
        <p>多参数RGB565图片格式分析工具</p>
    </div>
    
    <div class="controls">
        <div class="control-group">
            <input type="file" id="fileInput" class="file-input" accept="*">
            <label for="fileInput" class="file-label">📁 选择图片文件</label>
        </div>
        
        <div class="control-group">
            <label>文件偏移:</label>
            <input type="number" id="offset" value="0" min="0" max="100">
            <span>字节</span>
        </div>
        
        <div class="control-group">
            <label>宽度:</label>
            <input type="number" id="width" value="96" min="1" max="500">
            <label>高度:</label>
            <input type="number" id="height" value="100" min="1" max="500">
        </div>
        
        <div class="control-group">
            <label>字节序:</label>
            <select id="endian">
                <option value="little">小端序 (LE)</option>
                <option value="big">大端序 (BE)</option>
            </select>
        </div>
        
        <div class="control-group">
            <button onclick="analyzeFile()" style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">🔍 分析图片</button>
            <button onclick="tryAllDimensions()" style="padding: 10px 20px; background: #FF9800; color: white; border: none; border-radius: 5px; cursor: pointer;">🎯 尝试所有尺寸</button>
        </div>
    </div>
    
    <div id="hexDisplay" class="hex-display" style="display: none;"></div>
    <div id="results" class="results"></div>

    <script>
        let currentFileData = null;

        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    currentFileData = new Uint8Array(e.target.result);
                    displayHexData();
                    analyzeFile();
                };
                reader.readAsArrayBuffer(file);
            }
        });

        function displayHexData() {
            if (!currentFileData) return;
            
            const hexDisplay = document.getElementById('hexDisplay');
            const first64Bytes = currentFileData.slice(0, 64);
            const hexString = Array.from(first64Bytes)
                .map((b, i) => {
                    const hex = b.toString(16).padStart(2, '0').toUpperCase();
                    return (i % 16 === 0 ? '\n' : '') + hex + ' ';
                })
                .join('');
            
            hexDisplay.innerHTML = `<strong>文件头部 (前64字节):</strong><br>${hexString}`;
            hexDisplay.style.display = 'block';
        }

        function analyzeFile() {
            if (!currentFileData) {
                alert('请先选择文件');
                return;
            }

            const offset = parseInt(document.getElementById('offset').value);
            const width = parseInt(document.getElementById('width').value);
            const height = parseInt(document.getElementById('height').value);
            const endian = document.getElementById('endian').value;

            const result = processImageData(currentFileData, offset, width, height, endian);
            displayResult(result, `${width}x${height} (偏移:${offset}, ${endian})`);
        }

        function tryAllDimensions() {
            if (!currentFileData) {
                alert('请先选择文件');
                return;
            }

            document.getElementById('results').innerHTML = '';

            const pixelCount = (currentFileData.length - 0) / 2;
            const possibleDimensions = [];

            // 常见尺寸
            const commonSizes = [
                [96, 100], [100, 96], [80, 120], [120, 80],
                [64, 64], [128, 128], [32, 32], [16, 16],
                [240, 320], [320, 240], [160, 120], [120, 160],
                [48, 64], [64, 48], [40, 50], [50, 40]
            ];

            // 检查常见尺寸
            for (const [w, h] of commonSizes) {
                if (w * h <= pixelCount) {
                    possibleDimensions.push([w, h]);
                }
            }

            // 数学计算的尺寸
            for (let width = 1; width <= Math.sqrt(pixelCount); width++) {
                if (pixelCount % width === 0) {
                    const height = pixelCount / width;
                    if (height <= 500 && !possibleDimensions.some(d => d[0] === width && d[1] === height)) {
                        possibleDimensions.push([width, height]);
                    }
                }
            }

            // 尝试不同的偏移量和尺寸
            const offsets = [0, 4, 8, 12, 16];
            const endians = ['little', 'big'];

            let resultCount = 0;
            for (const offset of offsets) {
                for (const endian of endians) {
                    for (const [width, height] of possibleDimensions.slice(0, 8)) { // 限制数量
                        if (resultCount >= 16) break; // 最多显示16个结果
                        
                        const result = processImageData(currentFileData, offset, width, height, endian);
                        if (result) {
                            displayResult(result, `${width}x${height} (偏移:${offset}, ${endian})`, resultCount);
                            resultCount++;
                        }
                    }
                    if (resultCount >= 16) break;
                }
                if (resultCount >= 16) break;
            }
        }

        function processImageData(data, offset, width, height, endian) {
            const pixelCount = width * height;
            const requiredBytes = offset + pixelCount * 2;

            if (requiredBytes > data.length) {
                return null;
            }

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = width;
            canvas.height = height;
            
            const imageData = ctx.createImageData(width, height);
            const pixels = imageData.data;

            for (let i = 0; i < pixelCount; i++) {
                const byteIndex = offset + i * 2;
                
                let rgb565;
                if (endian === 'little') {
                    rgb565 = data[byteIndex] | (data[byteIndex + 1] << 8);
                } else {
                    rgb565 = (data[byteIndex] << 8) | data[byteIndex + 1];
                }

                const rgb = rgb565ToRgb888(rgb565);
                const pixelIndex = i * 4;

                // 检查透明色
                if (rgb565 === 0xF81F || rgb565 === 0x1FF8) {
                    pixels[pixelIndex] = 255;     // R
                    pixels[pixelIndex + 1] = 0;   // G
                    pixels[pixelIndex + 2] = 255; // B
                    pixels[pixelIndex + 3] = 128; // A (半透明)
                } else {
                    pixels[pixelIndex] = rgb.r;
                    pixels[pixelIndex + 1] = rgb.g;
                    pixels[pixelIndex + 2] = rgb.b;
                    pixels[pixelIndex + 3] = 255; // A
                }
            }

            ctx.putImageData(imageData, 0, 0);
            return canvas.toDataURL();
        }

        function rgb565ToRgb888(rgb565) {
            const r = (rgb565 >> 11) & 0x1F;
            const g = (rgb565 >> 5) & 0x3F;
            const b = rgb565 & 0x1F;
            
            return {
                r: Math.round((r * 255) / 31),
                g: Math.round((g * 255) / 63),
                b: Math.round((b * 255) / 31)
            };
        }

        function displayResult(dataUrl, description, index = 0) {
            const results = document.getElementById('results');
            
            const card = document.createElement('div');
            card.className = 'result-card';
            
            const img = document.createElement('img');
            img.src = dataUrl;
            img.alt = description;
            
            const title = document.createElement('h3');
            title.textContent = description;
            
            const info = document.createElement('p');
            info.textContent = `结果 #${index + 1}`;
            
            card.appendChild(img);
            card.appendChild(title);
            card.appendChild(info);
            
            results.appendChild(card);
        }
    </script>
</body>
</html>
