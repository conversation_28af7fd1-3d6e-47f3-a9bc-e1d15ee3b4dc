#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * MRP文件解析器 - 专门用于解析斯凯MiniJ平台的MRP游戏文件
 * 作者: Claude 4.0 sonnet
 */
class MRPExtractor {
    constructor(filePath) {
        this.filePath = filePath;
        this.buffer = null;
        this.fileInfo = {
            magic: '',
            version: 0,
            totalSize: 0,
            headerSize: 0
        };
        this.fileEntries = [];
    }

    /**
     * 读取MRP文件到内存
     */
    async loadFile() {
        try {
            this.buffer = fs.readFileSync(this.filePath);
            console.log(`📁 已加载文件: ${this.filePath} (${this.buffer.length} 字节)`);
            return true;
        } catch (error) {
            console.error(`❌ 无法读取文件: ${error.message}`);
            return false;
        }
    }

    /**
     * 解析MRP文件头
     */
    parseHeader() {
        if (!this.buffer || this.buffer.length < 16) {
            throw new Error('文件太小，不是有效的MRP文件');
        }

        // 读取魔数 "MRPG"
        this.fileInfo.magic = this.buffer.toString('ascii', 0, 4);
        if (this.fileInfo.magic !== 'MRPG') {
            throw new Error(`无效的MRP文件格式，期望 "MRPG"，实际得到 "${this.fileInfo.magic}"`);
        }

        // 读取版本信息（小端序）
        this.fileInfo.version = this.buffer.readUInt32LE(4);
        
        // 读取文件总大小（小端序）
        this.fileInfo.totalSize = this.buffer.readUInt32LE(8);
        
        // 读取头部大小或其他信息（小端序）
        this.fileInfo.headerSize = this.buffer.readUInt32LE(12);

        console.log('📋 文件头信息:');
        console.log(`   魔数: ${this.fileInfo.magic}`);
        console.log(`   版本: 0x${this.fileInfo.version.toString(16)}`);
        console.log(`   总大小: ${this.fileInfo.totalSize} 字节`);
        console.log(`   头部信息: 0x${this.fileInfo.headerSize.toString(16)}`);

        return true;
    }

    /**
     * 解析文件目录表
     */
    parseFileTable() {
        // 基于十六进制分析，文件目录表从偏移0xF0开始
        let offset = 0xF0;
        this.fileEntries = [];

        console.log('📂 解析文件目录表...');

        while (offset < this.buffer.length - 20) {
            try {
                // 读取文件名长度（4字节，小端序）
                const nameLength = this.buffer.readUInt32LE(offset);
                offset += 4;

                // 如果名称长度异常，可能已到达文件目录表末尾
                if (nameLength === 0 || nameLength > 256 || offset + nameLength > this.buffer.length) {
                    break;
                }

                // 读取文件名（以null结尾的字符串）
                let fileName = '';
                for (let i = 0; i < nameLength; i++) {
                    const char = this.buffer[offset + i];
                    if (char === 0) break; // 遇到null终止符
                    fileName += String.fromCharCode(char);
                }

                // 跳过文件名区域（包括null终止符和填充）
                offset += nameLength;

                // 读取文件偏移量（4字节，小端序）
                const fileOffset = this.buffer.readUInt32LE(offset);
                offset += 4;

                // 读取文件大小（4字节，小端序）
                const fileSize = this.buffer.readUInt32LE(offset);
                offset += 4;

                // 跳过保留字段（4字节，不是8字节）
                offset += 4;

                // 验证文件信息的合理性
                if (fileName && fileOffset > 0 && fileSize > 0 &&
                    fileOffset + fileSize <= this.buffer.length) {

                    this.fileEntries.push({
                        name: fileName,
                        offset: fileOffset,
                        size: fileSize
                    });

                    console.log(`   📄 ${fileName} (偏移: 0x${fileOffset.toString(16)}, 大小: ${fileSize} 字节)`);
                } else if (fileName) {
                    // 如果文件名存在但偏移量或大小异常，记录警告但继续解析
                    console.log(`   ⚠️  跳过异常条目: ${fileName} (偏移: 0x${fileOffset.toString(16)}, 大小: ${fileSize})`);
                }

                // 如果连续遇到无效条目，可能已到达目录表末尾
                if (!fileName || fileOffset === 0) {
                    break;
                }

            } catch (error) {
                // 如果解析出错，可能已到达目录表末尾
                console.log(`   ℹ️  解析结束于偏移 0x${offset.toString(16)}: ${error.message}`);
                break;
            }
        }

        console.log(`✅ 找到 ${this.fileEntries.length} 个有效文件`);
        return this.fileEntries.length > 0;
    }

    /**
     * 提取单个文件
     */
    extractFile(entry, outputDir) {
        try {
            const fileData = this.buffer.slice(entry.offset, entry.offset + entry.size);
            const outputPath = path.join(outputDir, entry.name);
            
            // 确保输出目录存在
            const dir = path.dirname(outputPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            fs.writeFileSync(outputPath, fileData);
            console.log(`   ✅ 已提取: ${entry.name}`);
            return true;
        } catch (error) {
            console.error(`   ❌ 提取失败 ${entry.name}: ${error.message}`);
            return false;
        }
    }

    /**
     * 提取所有文件
     */
    async extractAll(outputDir) {
        if (this.fileEntries.length === 0) {
            console.log('❌ 没有找到可提取的文件');
            return false;
        }

        // 创建输出目录
        const baseName = path.basename(this.filePath, '.mrp');
        const fullOutputDir = path.join(outputDir, baseName);
        
        if (!fs.existsSync(fullOutputDir)) {
            fs.mkdirSync(fullOutputDir, { recursive: true });
        }

        console.log(`🚀 开始提取到目录: ${fullOutputDir}`);

        let successCount = 0;
        for (const entry of this.fileEntries) {
            if (this.extractFile(entry, fullOutputDir)) {
                successCount++;
            }
        }

        console.log(`🎉 提取完成! 成功: ${successCount}/${this.fileEntries.length}`);
        return successCount > 0;
    }

    /**
     * 执行完整的解析和提取流程
     */
    async extract(outputDir = './extracted') {
        try {
            console.log('🐾 Claude 4.0 sonnet MRP解包工具启动!');
            console.log('=' .repeat(50));

            // 加载文件
            if (!await this.loadFile()) {
                return false;
            }

            // 解析文件头
            this.parseHeader();

            // 解析文件目录表
            if (!this.parseFileTable()) {
                console.log('❌ 无法解析文件目录表');
                return false;
            }

            // 提取所有文件
            return await this.extractAll(outputDir);

        } catch (error) {
            console.error(`❌ 解析错误: ${error.message}`);
            return false;
        }
    }
}

// 命令行接口
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('🐾 Claude 4.0 sonnet MRP解包工具');
        console.log('用法: node mrp-extractor.js <MRP文件路径> [输出目录]');
        console.log('');
        console.log('示例:');
        console.log('  node mrp-extractor.js game.mrp');
        console.log('  node mrp-extractor.js game.mrp ./output');
        process.exit(1);
    }

    const mrpFile = args[0];
    const outputDir = args[1] || './extracted';

    if (!fs.existsSync(mrpFile)) {
        console.error(`❌ 文件不存在: ${mrpFile}`);
        process.exit(1);
    }

    const extractor = new MRPExtractor(mrpFile);
    const success = await extractor.extract(outputDir);
    
    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = MRPExtractor;
