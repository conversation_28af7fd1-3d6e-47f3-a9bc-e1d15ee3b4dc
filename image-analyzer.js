#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * MRP图片格式分析工具 - 分析自定义图片格式
 * 作者: Claude 4.0 sonnet
 */
class ImageAnalyzer {
    constructor() {
        this.commonSizes = new Map();
        this.commonHeaders = new Map();
    }

    /**
     * 分析单个图片文件
     */
    analyzeFile(filePath) {
        try {
            const data = fs.readFileSync(filePath);
            const fileName = path.basename(filePath);
            
            console.log(`\n🔍 分析文件: ${fileName}`);
            console.log(`   大小: ${data.length} 字节`);
            
            // 记录常见大小
            const sizeCount = this.commonSizes.get(data.length) || 0;
            this.commonSizes.set(data.length, sizeCount + 1);
            
            // 分析可能的尺寸
            this.analyzePossibleDimensions(data.length);
            
            // 分析头部数据
            if (data.length >= 16) {
                const header = data.slice(0, 16);
                const headerHex = Array.from(header)
                    .map(b => b.toString(16).padStart(2, '0'))
                    .join(' ');
                console.log(`   头部: ${headerHex}`);
                
                // 记录常见头部模式
                const headerKey = headerHex.substring(0, 11); // 前4字节
                const headerCount = this.commonHeaders.get(headerKey) || 0;
                this.commonHeaders.set(headerKey, headerCount + 1);
                
                // 分析RGB565格式
                this.analyzeRGB565Pattern(data);
            }
            
            return true;
        } catch (error) {
            console.error(`❌ 分析失败 ${filePath}: ${error.message}`);
            return false;
        }
    }

    /**
     * 分析可能的图片尺寸
     */
    analyzePossibleDimensions(fileSize) {
        // 假设是16位色深 (2字节/像素)
        const pixelCount = fileSize / 2;
        
        console.log(`   像素数量 (16位): ${pixelCount}`);
        
        // 寻找可能的宽高组合
        const possibleDimensions = [];
        for (let width = 1; width <= Math.sqrt(pixelCount); width++) {
            if (pixelCount % width === 0) {
                const height = pixelCount / width;
                possibleDimensions.push(`${width}x${height}`);
            }
        }
        
        if (possibleDimensions.length > 0) {
            console.log(`   可能尺寸: ${possibleDimensions.join(', ')}`);
        }
    }

    /**
     * 分析RGB565模式
     */
    analyzeRGB565Pattern(data) {
        if (data.length < 4) return;
        
        // 读取前几个像素的RGB565值
        const pixels = [];
        for (let i = 0; i < Math.min(8, data.length / 2); i++) {
            const pixel = data.readUInt16LE(i * 2);
            
            // 解析RGB565
            const r = (pixel >> 11) & 0x1F;
            const g = (pixel >> 5) & 0x3F;
            const b = pixel & 0x1F;
            
            // 转换为RGB888
            const r8 = (r * 255) / 31;
            const g8 = (g * 255) / 63;
            const b8 = (b * 255) / 31;
            
            pixels.push({
                raw: pixel.toString(16).padStart(4, '0'),
                rgb: `(${Math.round(r8)},${Math.round(g8)},${Math.round(b8)})`
            });
        }
        
        console.log(`   前几个像素:`);
        pixels.forEach((pixel, index) => {
            console.log(`     ${index}: 0x${pixel.raw} -> RGB${pixel.rgb}`);
        });
    }

    /**
     * 批量分析目录中的图片文件
     */
    analyzeDirectory(inputDir) {
        console.log('🐾 Claude 4.0 sonnet 图片格式分析工具');
        console.log('=' .repeat(50));

        const imageFiles = [];
        this.findImageFiles(inputDir, imageFiles);

        if (imageFiles.length === 0) {
            console.log('❌ 没有找到图片文件');
            return false;
        }

        console.log(`📁 找到 ${imageFiles.length} 个图片文件`);

        // 分析前10个文件作为样本
        const sampleFiles = imageFiles.slice(0, 10);
        let successCount = 0;

        for (const filePath of sampleFiles) {
            if (this.analyzeFile(filePath)) {
                successCount++;
            }
        }

        // 显示统计信息
        this.showStatistics();

        return successCount > 0;
    }

    /**
     * 显示统计信息
     */
    showStatistics() {
        console.log('\n' + '=' .repeat(50));
        console.log('📊 统计信息:');
        
        console.log('\n🔢 常见文件大小:');
        const sortedSizes = Array.from(this.commonSizes.entries())
            .sort((a, b) => b[1] - a[1]);
        
        sortedSizes.slice(0, 10).forEach(([size, count]) => {
            console.log(`   ${size} 字节: ${count} 个文件`);
        });
        
        console.log('\n🎨 常见头部模式:');
        const sortedHeaders = Array.from(this.commonHeaders.entries())
            .sort((a, b) => b[1] - a[1]);
        
        sortedHeaders.slice(0, 5).forEach(([header, count]) => {
            console.log(`   ${header}: ${count} 个文件`);
        });
    }

    /**
     * 递归查找图片文件
     */
    findImageFiles(dir, fileList) {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                this.findImageFiles(fullPath, fileList);
            } else {
                const ext = path.extname(item).toLowerCase();
                if (['.bmp', '.png', '.jpg', '.jpeg', '.gif'].includes(ext)) {
                    fileList.push(fullPath);
                }
            }
        }
    }
}

// 命令行接口
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('🐾 Claude 4.0 sonnet 图片格式分析工具');
        console.log('用法: node image-analyzer.js <输入目录>');
        console.log('');
        console.log('示例:');
        console.log('  node image-analyzer.js ./decompressed');
        process.exit(1);
    }

    const inputDir = args[0];

    if (!fs.existsSync(inputDir)) {
        console.error(`❌ 输入目录不存在: ${inputDir}`);
        process.exit(1);
    }

    const analyzer = new ImageAnalyzer();
    const success = analyzer.analyzeDirectory(inputDir);
    
    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = ImageAnalyzer;
