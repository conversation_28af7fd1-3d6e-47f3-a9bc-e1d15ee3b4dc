#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const MRPExtractor = require('./mrp-extractor');

/**
 * 批量处理MRP文件的工具
 * 作者: Claude 4.0 sonnet
 */

async function extractAllMRPFiles(inputDir, outputDir = './extracted') {
    console.log('🐾 Claude 4.0 sonnet 批量MRP解包工具');
    console.log('=' .repeat(60));

    // 查找所有MRP文件
    const mrpFiles = [];
    
    function findMRPFiles(dir) {
        const items = fs.readdirSync(dir);
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                findMRPFiles(fullPath);
            } else if (path.extname(item).toLowerCase() === '.mrp') {
                mrpFiles.push(fullPath);
            }
        }
    }

    findMRPFiles(inputDir);

    if (mrpFiles.length === 0) {
        console.log('❌ 在指定目录中没有找到MRP文件');
        return false;
    }

    console.log(`📁 找到 ${mrpFiles.length} 个MRP文件:`);
    mrpFiles.forEach((file, index) => {
        console.log(`   ${index + 1}. ${path.relative(inputDir, file)}`);
    });
    console.log('');

    // 批量处理
    let successCount = 0;
    let totalCount = mrpFiles.length;

    for (let i = 0; i < mrpFiles.length; i++) {
        const mrpFile = mrpFiles[i];
        console.log(`\n🔄 处理文件 ${i + 1}/${totalCount}: ${path.basename(mrpFile)}`);
        console.log('-' .repeat(50));

        try {
            const extractor = new MRPExtractor(mrpFile);
            const success = await extractor.extract(outputDir);
            
            if (success) {
                successCount++;
                console.log(`✅ ${path.basename(mrpFile)} 处理完成`);
            } else {
                console.log(`❌ ${path.basename(mrpFile)} 处理失败`);
            }
        } catch (error) {
            console.error(`❌ ${path.basename(mrpFile)} 处理出错: ${error.message}`);
        }
    }

    console.log('\n' + '=' .repeat(60));
    console.log(`🎉 批量处理完成! 成功: ${successCount}/${totalCount}`);
    
    if (successCount > 0) {
        console.log(`📂 提取的文件保存在: ${path.resolve(outputDir)}`);
    }

    return successCount > 0;
}

// 命令行接口
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('🐾 Claude 4.0 sonnet 批量MRP解包工具');
        console.log('用法: node extract-all.js <输入目录> [输出目录]');
        console.log('');
        console.log('示例:');
        console.log('  node extract-all.js ./gwy');
        console.log('  node extract-all.js ./gwy ./output');
        console.log('  node extract-all.js . ./extracted');
        process.exit(1);
    }

    const inputDir = args[0];
    const outputDir = args[1] || './extracted';

    if (!fs.existsSync(inputDir)) {
        console.error(`❌ 输入目录不存在: ${inputDir}`);
        process.exit(1);
    }

    const success = await extractAllMRPFiles(inputDir, outputDir);
    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { extractAllMRPFiles };
