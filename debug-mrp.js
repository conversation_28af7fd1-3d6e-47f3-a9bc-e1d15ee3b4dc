#!/usr/bin/env node

const fs = require('fs');

/**
 * MRP文件调试工具 - 用于分析文件结构
 * 作者: Claude 4.0 sonnet
 */

function debugMRPFile(filePath) {
    console.log('🔍 Claude 4.0 sonnet MRP调试工具');
    console.log('=' .repeat(50));

    const buffer = fs.readFileSync(filePath);
    console.log(`📁 文件大小: ${buffer.length} 字节`);

    // 显示文件头
    console.log('\n📋 文件头分析:');
    console.log(`魔数: ${buffer.toString('ascii', 0, 4)}`);
    console.log(`版本: 0x${buffer.readUInt32LE(4).toString(16)}`);
    console.log(`总大小: ${buffer.readUInt32LE(8)}`);
    console.log(`头部信息: 0x${buffer.readUInt32LE(12).toString(16)}`);

    // 从0xF0开始详细分析目录表
    console.log('\n🔍 详细目录表分析 (从偏移0xF0开始):');
    let offset = 0xF0;
    let entryIndex = 0;

    while (offset < buffer.length - 20 && entryIndex < 20) { // 限制最多显示20个条目
        console.log(`\n--- 条目 ${entryIndex + 1} (偏移: 0x${offset.toString(16)}) ---`);

        try {
            // 显示原始字节
            const rawBytes = buffer.slice(offset, Math.min(offset + 32, buffer.length));
            console.log(`原始字节: ${rawBytes.toString('hex').match(/.{2}/g).join(' ')}`);

            // 读取文件名长度
            const nameLength = buffer.readUInt32LE(offset);
            console.log(`文件名长度: ${nameLength} (0x${nameLength.toString(16)})`);
            offset += 4;

            if (nameLength === 0 || nameLength > 256) {
                console.log('❌ 文件名长度异常，停止解析');
                break;
            }

            // 读取文件名
            let fileName = '';
            for (let i = 0; i < nameLength && offset + i < buffer.length; i++) {
                const char = buffer[offset + i];
                if (char === 0) break;
                fileName += String.fromCharCode(char);
            }
            console.log(`文件名: "${fileName}"`);

            // 跳过文件名区域，包括null终止符
            offset += nameLength;

            console.log(`读取偏移量位置: 0x${offset.toString(16)}`);

            // 读取偏移量和大小
            if (offset + 8 <= buffer.length) {
                const fileOffset = buffer.readUInt32LE(offset);
                const fileSize = buffer.readUInt32LE(offset + 4);
                console.log(`文件偏移: 0x${fileOffset.toString(16)} (${fileOffset})`);
                console.log(`文件大小: ${fileSize} 字节`);
                offset += 8;

                // 跳过保留字段（4字节，不是8字节）
                offset += 4;

                console.log(`下一个条目偏移: 0x${offset.toString(16)}`);

                // 显示下一个位置的字节
                if (offset < buffer.length) {
                    const nextBytes = buffer.slice(offset, Math.min(offset + 16, buffer.length));
                    console.log(`下一个位置字节: ${nextBytes.toString('hex').match(/.{2}/g).join(' ')}`);
                }

                // 验证文件数据
                if (fileOffset > 0 && fileSize > 0 && fileOffset + fileSize <= buffer.length) {
                    console.log('✅ 文件信息有效');

                    // 显示文件开头的几个字节
                    if (fileOffset < buffer.length) {
                        const fileStart = buffer.slice(fileOffset, Math.min(fileOffset + 16, buffer.length));
                        console.log(`文件开头: ${fileStart.toString('hex').match(/.{2}/g).join(' ')}`);
                    }
                } else {
                    console.log('❌ 文件信息无效');
                }
            }

        } catch (error) {
            console.log(`❌ 解析错误: ${error.message}`);
            break;
        }

        entryIndex++;
    }

    console.log(`\n📊 总共分析了 ${entryIndex} 个条目`);
}

// 命令行接口
const args = process.argv.slice(2);
if (args.length === 0) {
    console.log('用法: node debug-mrp.js <MRP文件路径>');
    process.exit(1);
}

const mrpFile = args[0];
if (!fs.existsSync(mrpFile)) {
    console.error(`❌ 文件不存在: ${mrpFile}`);
    process.exit(1);
}

debugMRPFile(mrpFile);
