const fs = require('fs');
const path = require('path');

class FixedImageConverter {
    constructor() {
        this.successCount = 0;
        this.failCount = 0;
        this.transparentColor = 0xF81F; // 常见的透明色 (洋红色)
    }

    /**
     * 改进的RGB565转RGB888解码
     */
    rgb565ToRgb888(rgb565) {
        // RGB565格式: RRRRRGGGGGGBBBBB
        const r = (rgb565 >> 11) & 0x1F;  // 提取R (5位)
        const g = (rgb565 >> 5) & 0x3F;   // 提取G (6位)
        const b = rgb565 & 0x1F;          // 提取B (5位)
        
        // 扩展到8位，使用更精确的算法
        return {
            r: Math.round((r * 255) / 31),  // 5位扩展到8位
            g: Math.round((g * 255) / 63),  // 6位扩展到8位
            b: Math.round((b * 255) / 31)   // 5位扩展到8位
        };
    }

    /**
     * 尝试不同的像素格式和字节序
     */
    detectBestFormat(data, width, height) {
        const formats = [
            { 
                name: 'RGB565_LE', 
                read: (data, i) => data.readUInt16LE(i),
                convert: (val) => this.rgb565ToRgb888(val)
            },
            { 
                name: 'RGB565_BE', 
                read: (data, i) => data.readUInt16BE(i),
                convert: (val) => this.rgb565ToRgb888(val)
            },
            { 
                name: 'BGR565_LE', 
                read: (data, i) => data.readUInt16LE(i),
                convert: (val) => this.bgr565ToRgb888(val)
            },
            { 
                name: 'RGB555_LE', 
                read: (data, i) => data.readUInt16LE(i),
                convert: (val) => this.rgb555ToRgb888(val)
            }
        ];

        // 分析前几个像素来判断最佳格式
        let bestFormat = formats[0]; // 默认RGB565_LE
        let bestScore = 0;

        for (const format of formats) {
            const score = this.scoreFormat(data, format, Math.min(100, width * height));
            if (score > bestScore) {
                bestScore = score;
                bestFormat = format;
            }
        }

        console.log(`   检测到最佳格式: ${bestFormat.name} (评分: ${bestScore.toFixed(2)})`);
        return bestFormat;
    }

    /**
     * 评估格式的合理性
     */
    scoreFormat(data, format, sampleCount) {
        let score = 0;
        const colors = new Set();
        let validPixels = 0;

        for (let i = 0; i < sampleCount * 2 && i + 1 < data.length; i += 2) {
            try {
                const rawValue = format.read(data, i);
                const rgb = format.convert(rawValue);
                
                // 检查颜色值是否合理
                if (rgb.r >= 0 && rgb.r <= 255 && 
                    rgb.g >= 0 && rgb.g <= 255 && 
                    rgb.b >= 0 && rgb.b <= 255) {
                    validPixels++;
                    colors.add(`${rgb.r},${rgb.g},${rgb.b}`);
                    
                    // 奖励合理的颜色分布
                    const brightness = (rgb.r + rgb.g + rgb.b) / 3;
                    if (brightness > 10 && brightness < 245) {
                        score += 1;
                    }
                }
            } catch (e) {
                // 格式错误，扣分
                score -= 10;
            }
        }

        // 颜色多样性奖励
        score += Math.min(colors.size / 10, 5);
        
        // 有效像素比例奖励
        score += (validPixels / sampleCount) * 10;

        return score;
    }

    /**
     * BGR565转RGB888
     */
    bgr565ToRgb888(bgr565) {
        const b = (bgr565 >> 11) & 0x1F;
        const g = (bgr565 >> 5) & 0x3F;
        const r = bgr565 & 0x1F;
        
        return {
            r: Math.round((r * 255) / 31),
            g: Math.round((g * 255) / 63),
            b: Math.round((b * 255) / 31)
        };
    }

    /**
     * RGB555转RGB888
     */
    rgb555ToRgb888(rgb555) {
        const r = (rgb555 >> 10) & 0x1F;
        const g = (rgb555 >> 5) & 0x1F;
        const b = rgb555 & 0x1F;
        
        return {
            r: Math.round((r * 255) / 31),
            g: Math.round((g * 255) / 31),
            b: Math.round((b * 255) / 31)
        };
    }

    /**
     * 智能尺寸检测
     */
    detectImageDimensions(fileSize) {
        const pixelCount = fileSize / 2;
        
        // 精确的已知尺寸（基于之前的分析）
        const knownDimensions = [
            [240, 320], [320, 240], [189, 200], [200, 189], [128, 150], [150, 128],
            [128, 128], [126, 145], [145, 126], [120, 134], [134, 120], [120, 128],
            [128, 120], [120, 80], [80, 120], [112, 133], [133, 112], [100, 50],
            [96, 100], [100, 96], [92, 96], [96, 92], [64, 96], [96, 64], [64, 64],
            [56, 70], [70, 56], [56, 64], [64, 56], [60, 60], [49, 78], [78, 49],
            [50, 80], [80, 50], [49, 76], [76, 49], [49, 60], [60, 49], [48, 49],
            [49, 48], [44, 49], [49, 44], [40, 50], [50, 40], [40, 45], [45, 40],
            [36, 44], [44, 36], [35, 58], [58, 35], [32, 32], [31, 40], [40, 31],
            [30, 60], [60, 30], [26, 77], [77, 26], [26, 30], [30, 26], [25, 20],
            [23, 23], [22, 26], [26, 22], [20, 32], [32, 20], [20, 20], [20, 10],
            [16, 16], [15, 16], [16, 15], [14, 16], [16, 14], [14, 14], [13, 35],
            [35, 13], [12, 16], [16, 12], [11, 22], [22, 11], [11, 14], [14, 11],
            [9, 11], [11, 9], [9, 9], [8, 8], [7, 9], [9, 7], [3, 7], [7, 3],
            [21, 3], [3, 21], [50, 25], [25, 50], [3, 4], [4, 3]
        ];
        
        // 检查精确匹配
        for (const [w, h] of knownDimensions) {
            if (w * h === pixelCount) {
                return { width: w, height: h, confidence: 'high' };
            }
        }
        
        // 寻找因数分解
        const sqrt = Math.sqrt(pixelCount);
        for (let width = Math.floor(sqrt); width >= 1; width--) {
            if (pixelCount % width === 0) {
                const height = pixelCount / width;
                return { width, height, confidence: 'medium' };
            }
        }
        
        return { width: Math.floor(sqrt), height: Math.ceil(sqrt), confidence: 'low' };
    }

    /**
     * 转换单个图片文件
     */
    convertImage(inputPath, outputPath) {
        try {
            const data = fs.readFileSync(inputPath);
            const fileName = path.basename(inputPath);
            
            console.log(`🔄 转换: ${fileName}`);
            
            // 检测图片尺寸
            const dimensions = this.detectImageDimensions(data.length);
            const { width, height, confidence } = dimensions;
            
            console.log(`   尺寸: ${width}x${height} (置信度: ${confidence})`);
            
            // 检测最佳像素格式
            const format = this.detectBestFormat(data, width, height);
            
            // 创建BMP文件
            const bmpData = this.createBmpFile(data, width, height, format);
            
            // 确保输出目录存在
            const outputDir = path.dirname(outputPath);
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }
            
            // 写入BMP文件
            const bmpOutputPath = outputPath.replace(/\.[^.]+$/, '.bmp');
            fs.writeFileSync(bmpOutputPath, bmpData);
            
            console.log(`   ✅ 转换成功: ${bmpOutputPath}`);
            this.successCount++;
            return true;

        } catch (error) {
            console.error(`   ❌ 转换失败: ${error.message}`);
            this.failCount++;
            return false;
        }
    }

    /**
     * 创建BMP文件
     */
    createBmpFile(data, width, height, format) {
        const rowBytes = width * 3;
        const paddedRowBytes = Math.ceil(rowBytes / 4) * 4;
        const imageSize = paddedRowBytes * height;
        const fileSize = 54 + imageSize;

        // 创建BMP文件头
        const header = Buffer.alloc(54);

        // BMP文件头 (14字节)
        header.write('BM', 0);                    // 文件类型
        header.writeUInt32LE(fileSize, 2);        // 文件大小
        header.writeUInt32LE(0, 6);               // 保留字段
        header.writeUInt32LE(54, 10);             // 数据偏移

        // DIB头 (40字节)
        header.writeUInt32LE(40, 14);             // DIB头大小
        header.writeInt32LE(width, 18);           // 图片宽度
        header.writeInt32LE(-height, 22);         // 图片高度 (负值=从上到下)
        header.writeUInt16LE(1, 26);              // 颜色平面数
        header.writeUInt16LE(24, 28);             // 每像素位数
        header.writeUInt32LE(0, 30);              // 压缩方式
        header.writeUInt32LE(imageSize, 34);      // 图像大小
        header.writeUInt32LE(2835, 38);           // 水平分辨率
        header.writeUInt32LE(2835, 42);           // 垂直分辨率
        header.writeUInt32LE(0, 46);              // 颜色数
        header.writeUInt32LE(0, 50);              // 重要颜色数

        // 创建图像数据
        const imageData = Buffer.alloc(imageSize);

        for (let y = 0; y < height; y++) {
            const rowStart = y * paddedRowBytes;

            for (let x = 0; x < width; x++) {
                const pixelIndex = y * width + x;
                const dataIndex = rowStart + x * 3;

                if (pixelIndex * 2 + 1 < data.length) {
                    const rawValue = format.read(data, pixelIndex * 2);
                    const rgb = format.convert(rawValue);

                    // BMP格式是BGR顺序
                    imageData[dataIndex] = rgb.b;     // B
                    imageData[dataIndex + 1] = rgb.g; // G
                    imageData[dataIndex + 2] = rgb.r; // R
                } else {
                    // 超出数据范围，填充黑色
                    imageData[dataIndex] = 0;
                    imageData[dataIndex + 1] = 0;
                    imageData[dataIndex + 2] = 0;
                }
            }
        }

        return Buffer.concat([header, imageData]);
    }

    /**
     * 批量转换目录中的图片
     */
    convertDirectory(inputDir, outputDir) {
        console.log(`🐾 Claude 4.0 sonnet 修复版图片转换工具`);
        console.log(`==================================================`);

        // 删除旧的输出目录
        if (fs.existsSync(outputDir)) {
            console.log(`🗑️  删除旧目录: ${outputDir}`);
            this.removeDirectory(outputDir);
        }

        // 创建新的输出目录
        fs.mkdirSync(outputDir, { recursive: true });

        // 查找所有图片文件
        const imageFiles = this.findImageFiles(inputDir);
        console.log(`📁 找到 ${imageFiles.length} 个图片文件`);

        // 转换每个文件
        for (const inputPath of imageFiles) {
            const relativePath = path.relative(inputDir, inputPath);
            const outputPath = path.join(outputDir, relativePath);
            this.convertImage(inputPath, outputPath);
        }

        // 生成报告
        console.log(`\n==================================================`);
        console.log(`🎉 转换完成! 成功: ${this.successCount}/${imageFiles.length}`);
        console.log(`📂 转换后的文件保存在: ${path.resolve(outputDir)}`);

        if (this.failCount > 0) {
            console.log(`⚠️  失败: ${this.failCount} 个文件`);
        }
    }

    /**
     * 递归查找图片文件
     */
    findImageFiles(dir) {
        const imageFiles = [];
        const imageExtensions = ['.bmp', '.png', '.jpg', '.jpeg'];

        const scanDirectory = (currentDir) => {
            const items = fs.readdirSync(currentDir);

            for (const item of items) {
                const fullPath = path.join(currentDir, item);
                const stat = fs.statSync(fullPath);

                if (stat.isDirectory()) {
                    scanDirectory(fullPath);
                } else if (stat.isFile()) {
                    const ext = path.extname(item).toLowerCase();
                    if (imageExtensions.includes(ext)) {
                        imageFiles.push(fullPath);
                    }
                }
            }
        };

        scanDirectory(dir);
        return imageFiles;
    }

    /**
     * 递归删除目录
     */
    removeDirectory(dir) {
        if (fs.existsSync(dir)) {
            const items = fs.readdirSync(dir);
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);

                if (stat.isDirectory()) {
                    this.removeDirectory(fullPath);
                } else {
                    fs.unlinkSync(fullPath);
                }
            }
            fs.rmdirSync(dir);
        }
    }
}

// 命令行使用
if (require.main === module) {
    const converter = new FixedImageConverter();

    const args = process.argv.slice(2);
    if (args.length < 2) {
        console.log('用法: node image-converter-fixed.js <输入目录> <输出目录>');
        console.log('示例: node image-converter-fixed.js decompressed_all converted_fixed');
        process.exit(1);
    }

    const [inputDir, outputDir] = args;
    converter.convertDirectory(inputDir, outputDir);
}

module.exports = FixedImageConverter;
