<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐾 Claude 4.0 sonnet MRP图片查看器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 10px 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .controls {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .file-input {
            display: none;
        }
        
        .file-label {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1em;
        }
        
        .file-label:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .image-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            transition: transform 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .image-card:hover {
            transform: scale(1.05);
        }
        
        .image-card img {
            max-width: 100%;
            max-height: 150px;
            border-radius: 8px;
            background: white;
            padding: 5px;
            image-rendering: pixelated;
        }
        
        .image-card h3 {
            margin: 10px 0 5px 0;
            font-size: 1em;
            word-break: break-all;
        }
        
        .image-card p {
            margin: 5px 0;
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .status {
            text-align: center;
            margin: 20px 0;
            font-size: 1.1em;
        }
        
        .error {
            color: #ff6b6b;
        }
        
        .success {
            color: #51cf66;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🐾 Claude 4.0 sonnet MRP图片查看器</h1>
        <p>专业的RGB565图片格式查看工具</p>
        <p>支持解压缩后的MRP游戏图片资源</p>
    </div>
    
    <div class="controls">
        <input type="file" id="fileInput" class="file-input" multiple accept=".bmp,.png,.jpg,.jpeg">
        <label for="fileInput" class="file-label">📁 选择图片文件</label>
    </div>
    
    <div id="status" class="status"></div>
    <div id="gallery" class="gallery"></div>

    <script>
        const fileInput = document.getElementById('fileInput');
        const gallery = document.getElementById('gallery');
        const status = document.getElementById('status');

        fileInput.addEventListener('change', handleFiles);

        function handleFiles(event) {
            const files = Array.from(event.target.files);
            
            if (files.length === 0) {
                return;
            }

            status.innerHTML = `<span class="success">正在处理 ${files.length} 个文件...</span>`;
            gallery.innerHTML = '';

            files.forEach((file, index) => {
                setTimeout(() => {
                    processFile(file);
                }, index * 100); // 延迟处理，避免阻塞
            });
        }

        function processFile(file) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const arrayBuffer = e.target.result;
                const uint8Array = new Uint8Array(arrayBuffer);
                
                // 检查是否为BMP文件
                if (uint8Array[0] === 0x42 && uint8Array[1] === 0x4D) {
                    // 这是BMP文件，直接显示
                    displayImage(file, URL.createObjectURL(file));
                } else {
                    // 尝试作为RGB565数据处理
                    processRGB565Data(file, uint8Array);
                }
            };
            
            reader.readAsArrayBuffer(file);
        }

        function processRGB565Data(file, data) {
            // 猜测图片尺寸
            const pixelCount = data.length / 2;
            const dimensions = guessImageDimensions(pixelCount);
            
            if (!dimensions) {
                console.error('无法确定图片尺寸:', file.name);
                return;
            }

            // 创建Canvas来渲染RGB565数据
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = dimensions.width;
            canvas.height = dimensions.height;
            
            const imageData = ctx.createImageData(dimensions.width, dimensions.height);
            const pixels = imageData.data;
            
            for (let i = 0; i < pixelCount; i++) {
                const rgb565 = data[i * 2] | (data[i * 2 + 1] << 8);
                const rgb = rgb565ToRgb888(rgb565);
                
                const pixelIndex = i * 4;
                
                // 检查透明色
                if (rgb565 === 0xF81F) {
                    pixels[pixelIndex] = 255;     // R
                    pixels[pixelIndex + 1] = 255; // G
                    pixels[pixelIndex + 2] = 255; // B
                    pixels[pixelIndex + 3] = 128; // A (半透明)
                } else {
                    pixels[pixelIndex] = rgb.r;
                    pixels[pixelIndex + 1] = rgb.g;
                    pixels[pixelIndex + 2] = rgb.b;
                    pixels[pixelIndex + 3] = 255; // A
                }
            }
            
            ctx.putImageData(imageData, 0, 0);
            
            // 转换为Data URL并显示
            const dataUrl = canvas.toDataURL();
            displayImage(file, dataUrl, dimensions);
        }

        function rgb565ToRgb888(rgb565) {
            const r = (rgb565 >> 11) & 0x1F;
            const g = (rgb565 >> 5) & 0x3F;
            const b = rgb565 & 0x1F;
            
            return {
                r: Math.round((r * 255) / 31),
                g: Math.round((g * 255) / 63),
                b: Math.round((b * 255) / 31)
            };
        }

        function guessImageDimensions(pixelCount) {
            // 常见的手机游戏图片尺寸
            const commonDimensions = [
                [240, 320], [320, 240],
                [128, 128], [64, 64], [32, 32], [16, 16],
                [100, 50], [50, 25], [25, 20], [20, 10],
                [7, 9], [9, 7], [3, 7], [21, 3],
                [40, 45], [45, 40], [30, 60], [60, 30]
            ];
            
            // 检查常见尺寸
            for (const [w, h] of commonDimensions) {
                if (w * h === pixelCount) {
                    return { width: w, height: h };
                }
            }
            
            // 寻找最接近正方形的尺寸
            const sqrt = Math.sqrt(pixelCount);
            for (let width = Math.floor(sqrt); width >= 1; width--) {
                if (pixelCount % width === 0) {
                    const height = pixelCount / width;
                    return { width, height };
                }
            }
            
            return null;
        }

        function displayImage(file, src, dimensions = null) {
            const card = document.createElement('div');
            card.className = 'image-card';
            
            const img = document.createElement('img');
            img.src = src;
            img.alt = file.name;
            
            const title = document.createElement('h3');
            title.textContent = file.name;
            
            const info = document.createElement('p');
            if (dimensions) {
                info.textContent = `${dimensions.width}x${dimensions.height} | ${file.size} 字节`;
            } else {
                info.textContent = `${file.size} 字节`;
            }
            
            card.appendChild(img);
            card.appendChild(title);
            card.appendChild(info);
            
            gallery.appendChild(card);
            
            // 更新状态
            const currentCount = gallery.children.length;
            status.innerHTML = `<span class="success">已处理 ${currentCount} 个文件</span>`;
        }
    </script>
</body>
</html>
