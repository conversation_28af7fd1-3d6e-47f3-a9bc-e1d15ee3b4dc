# MRP解包工具 🐾

> 由 **Claude 4.0 sonnet** 精心打造的专业MRP文件解包工具

一个强大的Node.js工具，用于解包和提取斯凯网络MiniJ平台的MRP游戏文件资源。

## 🌟 特性

- ✅ **完整解析**: 支持MRPG格式的完整解析
- 🚀 **批量处理**: 可以一次处理多个MRP文件
- 📁 **智能提取**: 自动创建目录结构并提取所有资源
- 🔍 **详细日志**: 提供详细的解析和提取过程信息
- 💪 **错误处理**: 完善的错误处理和用户友好的提示
- 🎯 **高效性能**: 基于Buffer的高效二进制文件处理

## 📋 系统要求

- Node.js 12.0.0 或更高版本
- 支持Windows、macOS、Linux

## 🚀 快速开始

### 安装

```bash
# 克隆或下载项目文件
# 确保你有以下文件：
# - mrp-extractor.js
# - extract-all.js  
# - package.json
```

### 基本使用

#### 1. 提取单个MRP文件

```bash
node mrp-extractor.js game.mrp
```

指定输出目录：
```bash
node mrp-extractor.js game.mrp ./output
```

#### 2. 批量提取多个MRP文件

```bash
# 提取当前目录下所有MRP文件
node extract-all.js .

# 提取指定目录下所有MRP文件
node extract-all.js ./gwy

# 指定输出目录
node extract-all.js ./gwy ./extracted
```

## 📂 输出结构

提取后的文件将按以下结构组织：

```
extracted/
├── game1/
│   ├── start.mr
│   ├── images/
│   │   ├── logo.bmp
│   │   ├── btn1.bmp
│   │   └── btn2.bmp
│   ├── sounds/
│   └── other_files/
├── game2/
│   └── ...
└── game3/
    └── ...
```

## 🔧 技术细节

### MRP文件格式

MRP文件是斯凯网络为MiniJ平台开发的游戏归档格式：

- **魔数**: `MRPG` (4字节)
- **版本**: 版本信息 (4字节)
- **大小**: 文件总大小 (4字节)
- **头部**: 头部元数据 (4字节)
- **目录表**: 文件目录信息
- **数据区**: 实际文件内容

### 支持的资源类型

- 🖼️ 图片文件 (.bmp, .jpg, .png等)
- 🎵 音频文件 (.wav, .mp3等)
- 📄 程序文件 (.mr, .ext等)
- 📋 配置文件
- 🎮 游戏资源文件

## 📖 API文档

### MRPExtractor类

```javascript
const MRPExtractor = require('./mrp-extractor');

const extractor = new MRPExtractor('game.mrp');
await extractor.extract('./output');
```

#### 方法

- `loadFile()`: 加载MRP文件到内存
- `parseHeader()`: 解析文件头信息
- `parseFileTable()`: 解析文件目录表
- `extractFile(entry, outputDir)`: 提取单个文件
- `extractAll(outputDir)`: 提取所有文件
- `extract(outputDir)`: 执行完整的解析和提取流程

## 🐛 故障排除

### 常见问题

1. **"无效的MRP文件格式"**
   - 确保文件是有效的MRP格式
   - 检查文件是否损坏

2. **"文件太小，不是有效的MRP文件"**
   - 检查文件是否完整下载
   - 确认文件大小正常

3. **"无法读取文件"**
   - 检查文件路径是否正确
   - 确认有读取权限

## 📝 更新日志

### v1.0.0
- ✨ 初始版本发布
- 🎯 支持MRPG格式解析
- 📁 批量处理功能
- 🔍 详细日志输出

## 👨‍💻 作者

**Claude 4.0 sonnet** - 专业的AI编程助手

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🙏 致谢

感谢斯凯网络创造了MiniJ平台和MRP格式，为早期手机游戏发展做出了重要贡献。

---

*用 ❤️ 和 🐾 由 Claude 4.0 sonnet 制作*
