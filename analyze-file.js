const fs = require('fs');

function analyzeFile(filePath) {
    try {
        const data = fs.readFileSync(filePath);
        console.log(`\n📁 分析文件: ${filePath}`);
        console.log(`📊 文件大小: ${data.length} 字节`);
        
        // 显示前32字节的十六进制
        console.log(`🔍 前32字节 (十六进制):`);
        const hexStr = Array.from(data.slice(0, 32))
            .map(b => b.toString(16).padStart(2, '0').toUpperCase())
            .join(' ');
        console.log(`   ${hexStr}`);
        
        // 检查是否有常见的文件头
        const header = data.slice(0, 4);
        const headerStr = header.toString('ascii');
        console.log(`📋 文件头: "${headerStr}" (${Array.from(header).map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')})`);
        
        // 分析可能的像素数据
        const pixelCount = data.length / 2;
        console.log(`🎨 假设RGB565: ${pixelCount} 像素`);
        
        // 尝试不同的尺寸
        const possibleDimensions = [];
        for (let width = 1; width <= Math.sqrt(pixelCount); width++) {
            if (pixelCount % width === 0) {
                const height = pixelCount / width;
                possibleDimensions.push(`${width}x${height}`);
            }
        }
        console.log(`📐 可能的尺寸: ${possibleDimensions.join(', ')}`);
        
        // 检查数据的统计信息
        const uniqueBytes = new Set(data);
        console.log(`🔢 唯一字节数: ${uniqueBytes.size}/256`);
        
        // 检查是否有重复模式
        const first16 = data.slice(0, 16);
        const second16 = data.slice(16, 32);
        const isRepeating = Buffer.compare(first16, second16) === 0;
        console.log(`🔄 前16字节是否重复: ${isRepeating ? '是' : '否'}`);
        
    } catch (error) {
        console.error(`❌ 分析失败: ${error.message}`);
    }
}

// 分析命令行参数指定的文件
if (process.argv.length > 2) {
    const filePath = process.argv[2];
    analyzeFile(filePath);
} else {
    console.log('用法: node analyze-file.js <文件路径>');
}
