#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * MRP图片转换器 - 将RGB565格式转换为PNG
 * 作者: Claude 4.0 sonnet
 */
class ImageConverter {
    constructor() {
        this.transparentColor = 0xF81F; // 洋红色作为透明色
        this.processedCount = 0;
        this.successCount = 0;
    }

    /**
     * RGB565转RGB888
     */
    rgb565ToRgb888(rgb565) {
        const r = (rgb565 >> 11) & 0x1F;
        const g = (rgb565 >> 5) & 0x3F;
        const b = rgb565 & 0x1F;
        
        return {
            r: Math.round((r * 255) / 31),
            g: Math.round((g * 255) / 63),
            b: Math.round((b * 255) / 31)
        };
    }

    /**
     * 猜测图片尺寸
     */
    guessImageDimensions(fileSize) {
        const pixelCount = fileSize / 2; // 16位 = 2字节/像素
        
        // 常见的手机游戏图片尺寸
        const commonDimensions = [
            [240, 320], [320, 240], // 屏幕尺寸
            [128, 128], [64, 64], [32, 32], [16, 16], // 正方形
            [100, 50], [50, 25], [25, 20], [20, 10], // 按钮
            [7, 9], [9, 7], [3, 7], [21, 3], // 小图标
            [40, 45], [45, 40], [30, 60], [60, 30] // 其他常见尺寸
        ];
        
        // 检查常见尺寸
        for (const [w, h] of commonDimensions) {
            if (w * h === pixelCount) {
                return { width: w, height: h, confidence: 'high' };
            }
        }
        
        // 寻找最接近正方形的尺寸
        const sqrt = Math.sqrt(pixelCount);
        for (let width = Math.floor(sqrt); width >= 1; width--) {
            if (pixelCount % width === 0) {
                const height = pixelCount / width;
                return { width, height, confidence: 'medium' };
            }
        }
        
        return { width: Math.floor(sqrt), height: Math.ceil(sqrt), confidence: 'low' };
    }

    /**
     * 创建BMP文件头
     */
    createBmpHeader(width, height) {
        // 计算行字节数（需要4字节对齐）
        const rowBytes = width * 3;
        const paddedRowBytes = Math.ceil(rowBytes / 4) * 4;
        const imageSize = paddedRowBytes * height;
        const fileSize = 54 + imageSize; // 文件头54字节 + 图像数据

        const header = Buffer.alloc(54);

        // BMP文件头 (14字节)
        header.write('BM', 0); // 文件类型
        header.writeUInt32LE(fileSize, 2); // 文件大小
        header.writeUInt32LE(0, 6); // 保留字段
        header.writeUInt32LE(54, 10); // 数据偏移量

        // DIB头 (40字节)
        header.writeUInt32LE(40, 14); // DIB头大小
        header.writeInt32LE(width, 18); // 图像宽度
        header.writeInt32LE(-height, 22); // 图像高度 (负值表示从上到下)
        header.writeUInt16LE(1, 26); // 颜色平面数
        header.writeUInt16LE(24, 28); // 每像素位数
        header.writeUInt32LE(0, 30); // 压缩方式
        header.writeUInt32LE(imageSize, 34); // 图像大小
        header.writeUInt32LE(2835, 38); // 水平分辨率 (72 DPI)
        header.writeUInt32LE(2835, 42); // 垂直分辨率 (72 DPI)
        header.writeUInt32LE(0, 46); // 颜色数
        header.writeUInt32LE(0, 50); // 重要颜色数

        return header;
    }

    /**
     * 转换单个图片文件
     */
    convertFile(inputPath, outputPath, forceDimensions = null) {
        try {
            console.log(`🔄 转换: ${path.basename(inputPath)}`);

            const data = fs.readFileSync(inputPath);

            // 确定图片尺寸
            let dimensions;
            if (forceDimensions) {
                dimensions = forceDimensions;
            } else {
                dimensions = this.guessImageDimensions(data.length);
            }

            const { width, height, confidence } = dimensions;
            console.log(`   尺寸: ${width}x${height} (置信度: ${confidence})`);

            // 计算行字节数（需要4字节对齐）
            const rowBytes = width * 3;
            const paddedRowBytes = Math.ceil(rowBytes / 4) * 4;
            const imageSize = paddedRowBytes * height;

            // 创建BMP数据
            const bmpHeader = this.createBmpHeader(width, height);
            const imageData = Buffer.alloc(imageSize);

            // 填充图像数据
            for (let y = 0; y < height; y++) {
                const rowStart = y * paddedRowBytes;

                for (let x = 0; x < width; x++) {
                    const pixelIndex = y * width + x;
                    const dataIndex = rowStart + x * 3;

                    if (pixelIndex * 2 + 1 < data.length) {
                        // 读取RGB565像素 (小端序)
                        const rgb565 = data.readUInt16LE(pixelIndex * 2);
                        const rgb = this.rgb565ToRgb888(rgb565);

                        // 检查是否为透明色
                        if (rgb565 === this.transparentColor) {
                            // 透明色设为白色
                            imageData[dataIndex] = 255;     // B
                            imageData[dataIndex + 1] = 255; // G
                            imageData[dataIndex + 2] = 255; // R
                        } else {
                            // BMP格式是BGR顺序
                            imageData[dataIndex] = rgb.b;
                            imageData[dataIndex + 1] = rgb.g;
                            imageData[dataIndex + 2] = rgb.r;
                        }
                    } else {
                        // 超出数据范围，填充黑色
                        imageData[dataIndex] = 0;
                        imageData[dataIndex + 1] = 0;
                        imageData[dataIndex + 2] = 0;
                    }
                }

                // 行填充已经在Buffer.alloc时处理了（自动填充0）
            }

            // 写入BMP文件
            const bmpData = Buffer.concat([bmpHeader, imageData]);
            fs.writeFileSync(outputPath, bmpData);

            console.log(`   ✅ 转换成功: ${outputPath}`);
            this.successCount++;
            return true;

        } catch (error) {
            console.error(`   ❌ 转换失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 批量转换目录中的图片文件
     */
    async convertDirectory(inputDir, outputDir) {
        console.log('🐾 Claude 4.0 sonnet 图片转换工具');
        console.log('=' .repeat(50));

        // 创建输出目录
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        // 查找所有图片文件
        const imageFiles = [];
        this.findImageFiles(inputDir, imageFiles);

        if (imageFiles.length === 0) {
            console.log('❌ 没有找到图片文件');
            return false;
        }

        console.log(`📁 找到 ${imageFiles.length} 个图片文件`);

        // 批量转换
        for (const inputPath of imageFiles) {
            this.processedCount++;
            
            // 生成输出路径
            const relativePath = path.relative(inputDir, inputPath);
            const outputPath = path.join(outputDir, relativePath.replace(/\.[^.]+$/, '.bmp'));
            
            // 确保输出目录存在
            const outputDirPath = path.dirname(outputPath);
            if (!fs.existsSync(outputDirPath)) {
                fs.mkdirSync(outputDirPath, { recursive: true });
            }

            // 转换文件
            await this.convertFile(inputPath, outputPath);
        }

        console.log('\n' + '=' .repeat(50));
        console.log(`🎉 转换完成! 成功: ${this.successCount}/${this.processedCount}`);
        
        if (this.successCount > 0) {
            console.log(`📂 转换后的文件保存在: ${path.resolve(outputDir)}`);
        }

        return this.successCount > 0;
    }

    /**
     * 递归查找图片文件
     */
    findImageFiles(dir, fileList) {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                this.findImageFiles(fullPath, fileList);
            } else {
                const ext = path.extname(item).toLowerCase();
                if (['.bmp', '.png', '.jpg', '.jpeg', '.gif'].includes(ext)) {
                    fileList.push(fullPath);
                }
            }
        }
    }
}

// 命令行接口
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('🐾 Claude 4.0 sonnet 图片转换工具');
        console.log('用法: node image-converter.js <输入目录> [输出目录]');
        console.log('');
        console.log('示例:');
        console.log('  node image-converter.js ./decompressed');
        console.log('  node image-converter.js ./decompressed ./converted');
        process.exit(1);
    }

    const inputDir = args[0];
    const outputDir = args[1] || './converted';

    if (!fs.existsSync(inputDir)) {
        console.error(`❌ 输入目录不存在: ${inputDir}`);
        process.exit(1);
    }

    const converter = new ImageConverter();
    const success = await converter.convertDirectory(inputDir, outputDir);
    
    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = ImageConverter;
