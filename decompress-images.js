const fs = require('fs');
const path = require('path');
const zlib = require('zlib');

class ImageDecompressor {
    constructor() {
        this.successCount = 0;
        this.failCount = 0;
    }

    /**
     * 检查文件是否为GZIP压缩
     */
    isGzipCompressed(data) {
        return data.length >= 2 && data[0] === 0x1f && data[1] === 0x8b;
    }

    /**
     * 解压缩单个文件
     */
    decompressFile(inputPath, outputPath) {
        try {
            const data = fs.readFileSync(inputPath);
            
            if (!this.isGzipCompressed(data)) {
                console.log(`   ⚠️  跳过非压缩文件: ${path.basename(inputPath)}`);
                // 直接复制文件
                fs.writeFileSync(outputPath, data);
                return true;
            }

            console.log(`🔄 解压缩: ${path.basename(inputPath)}`);
            console.log(`   原始大小: ${data.length} 字节`);

            // 使用zlib解压缩
            const decompressed = zlib.gunzipSync(data);
            console.log(`   解压后大小: ${decompressed.length} 字节`);
            
            // 保存解压缩后的文件
            fs.writeFileSync(outputPath, decompressed);
            
            console.log(`   ✅ 解压成功: ${outputPath}`);
            this.successCount++;
            return true;

        } catch (error) {
            console.error(`   ❌ 解压失败: ${error.message}`);
            this.failCount++;
            return false;
        }
    }

    /**
     * 批量解压缩目录中的文件
     */
    decompressDirectory(inputDir, outputDir) {
        console.log(`🐾 Claude 4.0 sonnet 图片解压缩工具`);
        console.log(`==================================================`);
        
        if (!fs.existsSync(inputDir)) {
            console.error(`❌ 输入目录不存在: ${inputDir}`);
            return;
        }

        // 创建输出目录
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        // 递归处理所有文件
        this.processDirectory(inputDir, outputDir);

        console.log(`\n==================================================`);
        console.log(`🎉 解压完成! 成功: ${this.successCount}, 失败: ${this.failCount}`);
        console.log(`📂 解压后的文件保存在: ${path.resolve(outputDir)}`);
    }

    /**
     * 递归处理目录
     */
    processDirectory(inputDir, outputDir) {
        const items = fs.readdirSync(inputDir);
        
        for (const item of items) {
            const inputPath = path.join(inputDir, item);
            const outputPath = path.join(outputDir, item);
            
            const stat = fs.statSync(inputPath);
            
            if (stat.isDirectory()) {
                // 创建子目录并递归处理
                if (!fs.existsSync(outputPath)) {
                    fs.mkdirSync(outputPath, { recursive: true });
                }
                this.processDirectory(inputPath, outputPath);
            } else if (stat.isFile()) {
                // 处理文件
                this.decompressFile(inputPath, outputPath);
            }
        }
    }
}

// 命令行使用
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length < 2) {
        console.log('用法: node decompress-images.js <输入目录> <输出目录>');
        console.log('示例: node decompress-images.js gcsjol_extracted decompressed');
        process.exit(1);
    }

    const [inputDir, outputDir] = args;
    const decompressor = new ImageDecompressor();
    decompressor.decompressDirectory(inputDir, outputDir);
}

module.exports = ImageDecompressor;
